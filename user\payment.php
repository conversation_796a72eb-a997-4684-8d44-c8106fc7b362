<?php
$page_title = 'Payment';
$show_nav = true;
$include_razorpay = true;
require_once '../includes/header.php';

// Check if order data exists
if (!isset($_SESSION['order_data'])) {
    $_SESSION['error_message'] = 'No order data found. Please fill the form first.';
    redirect('../user/form.php');
}

$order_data = $_SESSION['order_data'];

// Create Razorpay order
try {
    $conn = getDBConnection();

    // Generate payment ID
    $payment_id = 'PAY_' . generateUUID();

    // Insert payment record
    $stmt = $conn->prepare("
        INSERT INTO payments (payment_id, user_id, amount, status)
        VALUES (?, ?, ?, 'created')
    ");
    $stmt->execute([$payment_id, $order_data['user_id'], $order_data['amount']]);

    // Store payment ID in session
    $_SESSION['payment_id'] = $payment_id;

    // Log payment creation
    logActivity("Payment created: $payment_id for user: " . $order_data['name'], 'user', $order_data['user_id']);

} catch (Exception $e) {
    $_SESSION['error_message'] = 'Payment initialization failed. Please try again.';
    logActivity("Payment initialization error: " . $e->getMessage());
    redirect('../user/form.php');
}
?>

<div class="card">
    <div class="card-header">
        <h2>Payment</h2>
    </div>
    <div class="card-body">
        <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 5px; margin-bottom: 2rem;">
            <h3 style="margin: 0 0 1rem 0; color: #667eea;">Order Details</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                <div>
                    <strong>Name:</strong> <?php echo htmlspecialchars($order_data['name']); ?>
                </div>
                <div>
                    <strong>UID:</strong> <?php echo htmlspecialchars($order_data['uid']); ?>
                </div>
                <div>
                    <strong>Phone:</strong> <?php echo htmlspecialchars($order_data['phone']); ?>
                </div>
                <div>
                    <strong>Amount:</strong> <?php echo formatCurrency($order_data['amount']); ?>
                </div>
            </div>
        </div>

        <?php if (PAYMENT_MODE === 'demo'): ?>
            <div style="background: #f8d7da; padding: 1rem; border-radius: 5px; border-left: 4px solid #dc3545; margin: 1rem 0;">
                <h4 style="margin: 0 0 0.5rem 0; color: #721c24;">⚠️ Demo Mode Active</h4>
                <p style="margin: 0; color: #721c24;">This is demonstration mode. No real payment will be processed.</p>
            </div>
        <?php endif; ?>

        <div style="text-align: center;">
            <button id="pay-button" class="btn btn-success" style="font-size: 1.2rem; padding: 1rem 2rem;">
                <?php echo PAYMENT_MODE === 'demo' ? 'Demo Payment' : 'Pay'; ?> <?php echo formatCurrency($order_data['amount']); ?>
            </button>
        </div>

        <div style="text-align: center; margin-top: 1rem;">
            <a href="form.php" class="btn btn-secondary">Back to Form</a>
        </div>

        <div style="margin-top: 2rem; padding: 1rem; background: #e7f3ff; border-radius: 5px; border-left: 4px solid #007bff;">
            <h4 style="margin: 0 0 0.5rem 0; color: #007bff;">Payment Information</h4>
            <ul style="margin: 0; padding-left: 1.5rem; color: #666;">
                <li>Your payment is secured by Razorpay</li>
                <li>You can pay using UPI, Cards, Net Banking, or Wallets</li>
                <li>After successful payment, you'll receive a unique token</li>
                <li>Show this token at the mess counter to collect your food</li>
                <li>Payment will be verified automatically after completion</li>
            </ul>
        </div>

        <div style="margin-top: 1rem; padding: 1rem; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
            <h4 style="margin: 0 0 0.5rem 0; color: #856404;">🔒 Security Features</h4>
            <ul style="margin: 0; padding-left: 1.5rem; color: #856404;">
                <li>All payments are processed through secure Razorpay gateway</li>
                <li>Payment verification using cryptographic signatures</li>
                <li>No card details are stored on our servers</li>
                <li>Real-time payment status updates</li>
            </ul>
        </div>
    </div>
</div>

<script>
document.getElementById('pay-button').addEventListener('click', function() {
    // Show loading
    this.disabled = true;
    this.innerHTML = '<span class="loading"></span> Creating Order...';

    // Create Razorpay order on server
    fetch('../api/create_order.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Initialize Razorpay payment
            var options = {
                "key": data.key,
                "amount": data.amount,
                "currency": data.currency,
                "name": "<?php echo SITE_NAME; ?>",
                "description": "Mess Food Payment",
                "order_id": data.order_id,
                "handler": function (response) {
                    // Payment successful - verify on server
                    var form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '../api/payment_callback.php';

                    var fields = {
                        'razorpay_payment_id': response.razorpay_payment_id,
                        'razorpay_order_id': response.razorpay_order_id,
                        'razorpay_signature': response.razorpay_signature,
                        'payment_id': data.payment_id
                    };

                    for (var key in fields) {
                        var input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = key;
                        input.value = fields[key];
                        form.appendChild(input);
                    }

                    document.body.appendChild(form);

                    // Show processing message
                    document.getElementById('pay-button').innerHTML = '<span class="loading"></span> Verifying Payment...';

                    // Submit form for verification
                    fetch('../api/payment_callback.php', {
                        method: 'POST',
                        body: new FormData(form)
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            window.location.href = result.redirect_url;
                        } else {
                            alert('Payment verification failed: ' + result.error);
                            window.location.href = 'payment_failed.php?error=' + encodeURIComponent(result.error);
                        }
                    })
                    .catch(error => {
                        console.error('Verification error:', error);
                        alert('Payment verification failed. Please contact support.');
                        window.location.href = 'payment_failed.php?error=verification_failed';
                    });
                },
                "prefill": data.prefill,
                "theme": {
                    "color": "#667eea"
                },
                "modal": {
                    "ondismiss": function() {
                        // Payment cancelled
                        document.getElementById('pay-button').disabled = false;
                        document.getElementById('pay-button').innerHTML = 'Pay <?php echo formatCurrency($order_data['amount']); ?>';
                    }
                }
            };

            // Reset button text
            document.getElementById('pay-button').innerHTML = 'Pay <?php echo formatCurrency($order_data['amount']); ?>';
            document.getElementById('pay-button').disabled = false;

            // Open Razorpay checkout
            var rzp = new Razorpay(options);
            rzp.open();
        } else {
            alert('Failed to create payment order: ' + data.error);
            document.getElementById('pay-button').disabled = false;
            document.getElementById('pay-button').innerHTML = 'Pay <?php echo formatCurrency($order_data['amount']); ?>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to create payment order. Please try again.');
        document.getElementById('pay-button').disabled = false;
        document.getElementById('pay-button').innerHTML = 'Pay <?php echo formatCurrency($order_data['amount']); ?>';
    });
});
</script>

<?php require_once '../includes/footer.php'; ?>
